#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包后SSL功能测试脚本
用于测试打包后的程序是否能正常进行SSL连接
"""

import sys
import os

def test_ssl_imports():
    """测试SSL相关模块导入"""
    print("=== 测试SSL模块导入 ===")
    
    modules_to_test = [
        'ssl',
        '_ssl', 
        '_hashlib',
        'socket',
        'requests',
        'urllib3',
        'certifi'
    ]
    
    success_count = 0
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {module_name} 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"✗ {module_name} 导入失败: {e}")
        except Exception as e:
            print(f"✗ {module_name} 导入异常: {e}")
    
    print(f"导入成功率: {success_count}/{len(modules_to_test)}")
    print()
    return success_count == len(modules_to_test)

def test_ssl_basic_functionality():
    """测试SSL基本功能"""
    print("=== 测试SSL基本功能 ===")
    
    try:
        import ssl
        
        # 测试SSL版本信息
        print(f"SSL版本: {ssl.OPENSSL_VERSION}")
        
        # 测试创建SSL上下文
        context = ssl.create_default_context()
        print("✓ SSL上下文创建成功")
        
        # 测试证书路径
        import certifi
        cert_path = certifi.where()
        print(f"✓ 证书路径: {cert_path}")
        
        if os.path.exists(cert_path):
            print("✓ 证书文件存在")
        else:
            print("✗ 证书文件不存在")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ SSL基本功能测试失败: {e}")
        return False

def test_https_request():
    """测试HTTPS请求"""
    print("=== 测试HTTPS请求 ===")
    
    try:
        import requests
        import urllib3
        
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 测试简单的HTTPS请求
        test_urls = [
            "https://www.baidu.com",
            "https://httpbin.org/get"
        ]
        
        success_count = 0
        for url in test_urls:
            try:
                print(f"测试: {url}")
                response = requests.get(url, timeout=10, verify=False)
                if response.status_code == 200:
                    print(f"✓ 请求成功，状态码: {response.status_code}")
                    success_count += 1
                else:
                    print(f"✗ 请求失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"✗ 请求异常: {e}")
        
        print(f"HTTPS请求成功率: {success_count}/{len(test_urls)}")
        return success_count > 0
        
    except Exception as e:
        print(f"✗ HTTPS请求测试失败: {e}")
        return False

def test_novel_finder_ssl():
    """测试NovelFinder的SSL功能"""
    print("=== 测试NovelFinder SSL功能 ===")
    
    try:
        # 临时设置日志级别为error，避免过多输出
        from config import Config
        config = Config()
        original_log_level = config.get("log_level", "error")
        config.set("log_level", "error")
        
        try:
            from novel_finder import NovelFinder
            
            # 创建NovelFinder实例
            finder = NovelFinder()
            print("✓ NovelFinder实例创建成功")
            
            # 测试搜索功能（这会涉及到网络请求）
            print("测试搜索功能...")
            results = finder.search("测试")
            
            if results:
                print(f"✓ 搜索成功，找到 {len(results)} 个结果")
                return True
            else:
                print("✗ 搜索无结果（可能是网络问题）")
                return False
                
        finally:
            # 恢复原始日志级别
            config.set("log_level", original_log_level)
            
    except Exception as e:
        print(f"✗ NovelFinder SSL测试失败: {e}")
        return False

def main():
    """主函数"""
    print("打包后SSL功能测试")
    print("=" * 50)
    print(f"Python可执行文件: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    print("=" * 50)
    print()
    
    # 运行所有测试
    tests = [
        ("SSL模块导入", test_ssl_imports),
        ("SSL基本功能", test_ssl_basic_functionality), 
        ("HTTPS请求", test_https_request),
        ("NovelFinder SSL", test_novel_finder_ssl)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"开始测试: {test_name}")
        try:
            if test_func():
                print(f"✓ {test_name} 测试通过")
                passed_tests += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
        print()
    
    # 输出总结
    print("=" * 50)
    print(f"测试总结: {passed_tests}/{total_tests} 个测试通过")
    
    if passed_tests == total_tests:
        print("✓ 所有SSL功能测试通过！")
        return 0
    elif passed_tests >= total_tests // 2:
        print("⚠ 部分SSL功能正常，但仍有问题需要解决")
        return 1
    else:
        print("✗ 大部分SSL功能异常，需要检查打包配置")
        return 2

if __name__ == "__main__":
    exit_code = main()
    
    print("\n建议:")
    if exit_code == 0:
        print("- SSL功能正常，可以正常使用")
    elif exit_code == 1:
        print("- 检查网络连接和防火墙设置")
        print("- 运行 ssl_environment_check.py 进行详细检查")
    else:
        print("- 检查PyInstaller的spec文件配置")
        print("- 确保所有SSL相关模块都被正确包含")
        print("- 运行 ssl_environment_check.py 对比打包前后的差异")
    
    sys.exit(exit_code)
