#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL环境检测工具
用于检测打包前后的SSL环境是否正常
"""

import sys
import os
import ssl
import socket
import platform

def check_python_info():
    """检查Python环境信息"""
    print("=== Python环境信息 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python可执行文件路径: {sys.executable}")
    print(f"Python安装目录: {os.path.dirname(sys.executable)}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.machine()}")
    print()

def check_ssl_module():
    """检查SSL模块"""
    print("=== SSL模块检查 ===")
    
    try:
        import ssl
        print(f"✓ SSL模块可用")
        print(f"SSL版本: {ssl.OPENSSL_VERSION}")
        print(f"SSL版本号: {ssl.OPENSSL_VERSION_NUMBER}")
        print(f"SSL版本信息: {ssl.OPENSSL_VERSION_INFO}")
        
        # 检查SSL上下文
        try:
            context = ssl.create_default_context()
            print(f"✓ 默认SSL上下文创建成功")
            print(f"协议: {context.protocol}")
            print(f"验证模式: {context.verify_mode}")
        except Exception as e:
            print(f"✗ 创建SSL上下文失败: {e}")
            
    except ImportError as e:
        print(f"✗ SSL模块不可用: {e}")
    except Exception as e:
        print(f"✗ SSL模块检查失败: {e}")
    print()

def check_ssl_files():
    """检查SSL相关文件"""
    print("=== SSL文件检查 ===")
    
    python_dir = os.path.dirname(sys.executable)
    
    # 检查可能的SSL DLL文件
    ssl_files = [
        'libssl-1_1.dll',
        'libcrypto-1_1.dll', 
        'libssl-1_1-x64.dll',
        'libcrypto-1_1-x64.dll',
        'ssleay32.dll',
        'libeay32.dll'
    ]
    
    found_files = []
    
    # 在Python目录中查找
    for filename in ssl_files:
        file_path = os.path.join(python_dir, filename)
        if os.path.exists(file_path):
            found_files.append(file_path)
            print(f"✓ 找到SSL文件: {file_path}")
        
        # 在DLLs目录中查找
        dlls_path = os.path.join(python_dir, 'DLLs', filename)
        if os.path.exists(dlls_path):
            found_files.append(dlls_path)
            print(f"✓ 找到SSL文件: {dlls_path}")
    
    if not found_files:
        print("✗ 未找到SSL DLL文件")
    else:
        print(f"总共找到 {len(found_files)} 个SSL文件")
    print()

def check_certifi():
    """检查certifi证书"""
    print("=== 证书检查 ===")
    
    try:
        import certifi
        cert_path = certifi.where()
        print(f"✓ certifi可用，版本: {certifi.__version__}")
        print(f"证书文件路径: {cert_path}")
        
        if os.path.exists(cert_path):
            file_size = os.path.getsize(cert_path)
            print(f"✓ 证书文件存在，大小: {file_size} 字节")
        else:
            print(f"✗ 证书文件不存在: {cert_path}")
            
    except ImportError:
        print("✗ certifi不可用")
    except Exception as e:
        print(f"✗ certifi检查失败: {e}")
    print()

def check_requests():
    """检查requests库"""
    print("=== requests库检查 ===")
    
    try:
        import requests
        import urllib3
        print(f"✓ requests可用，版本: {requests.__version__}")
        print(f"✓ urllib3可用，版本: {urllib3.__version__}")
        
        # 测试简单的HTTPS请求
        try:
            print("测试HTTPS请求...")
            response = requests.get('https://httpbin.org/get', timeout=10, verify=False)
            print(f"✓ HTTPS请求成功，状态码: {response.status_code}")
        except Exception as e:
            print(f"✗ HTTPS请求失败: {e}")
            
    except ImportError as e:
        print(f"✗ requests不可用: {e}")
    except Exception as e:
        print(f"✗ requests检查失败: {e}")
    print()

def check_socket():
    """检查socket连接"""
    print("=== Socket连接检查 ===")
    
    try:
        # 测试基本的socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        # 测试连接到百度
        try:
            sock.connect(('www.baidu.com', 80))
            print("✓ HTTP socket连接成功 (www.baidu.com:80)")
            sock.close()
        except Exception as e:
            print(f"✗ HTTP socket连接失败: {e}")
        
        # 测试SSL socket连接
        try:
            context = ssl.create_default_context()
            with socket.create_connection(('www.baidu.com', 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname='www.baidu.com') as ssock:
                    print("✓ HTTPS socket连接成功 (www.baidu.com:443)")
        except Exception as e:
            print(f"✗ HTTPS socket连接失败: {e}")
            
    except Exception as e:
        print(f"✗ Socket检查失败: {e}")
    print()

def check_encoding():
    """检查编码支持"""
    print("=== 编码支持检查 ===")
    
    encodings_to_test = ['utf-8', 'gbk', 'gb2312', 'big5', 'gb18030']
    
    for encoding in encodings_to_test:
        try:
            import codecs
            codecs.lookup(encoding)
            print(f"✓ {encoding} 编码可用")
        except LookupError:
            print(f"✗ {encoding} 编码不可用")
        except Exception as e:
            print(f"✗ {encoding} 编码检查失败: {e}")
    print()

def main():
    """主函数"""
    print("SSL环境检测工具")
    print("=" * 60)
    print("此工具将检测当前环境的SSL相关配置")
    print("请在打包前后都运行此脚本进行对比")
    print("=" * 60)
    print()
    
    check_python_info()
    check_ssl_module()
    check_ssl_files()
    check_certifi()
    check_requests()
    check_socket()
    check_encoding()
    
    print("=== 检测完成 ===")
    print("如果打包后某些检查失败，请检查PyInstaller的spec文件配置")
    print("确保所有必要的模块和文件都被正确包含")

if __name__ == "__main__":
    main()
