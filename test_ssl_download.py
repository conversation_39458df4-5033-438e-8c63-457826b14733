#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL下载测试工具
专门测试HTTPS下载功能
"""

import requests
import urllib3
import ssl
import socket
from urllib.parse import urlparse

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_problematic_url():
    """测试出现问题的URL"""
    
    # 你遇到问题的URL
    test_url = "https://pdf1.webgetstore.com/2025/05/20/850ef01be3eb68b4e0a95bcdb89ae6d2.txt?sg=4a0f87ffdf1967c4581721e5acc0af05&e=683734b5&fileName=%5B%E5%BA%9F%E6%96%87%20%E5%AE%8C%E7%BB%93%5D%E6%9C%AB%E6%97%A5%E4%BA%9A%E7%A7%8D%EF%BC%88np%EF%BC%89%E4%BD%9C%E8%80%85%EF%BC%9A%E9%B8%A3%E4%BB%A5%E5%92%8C%E9%B8%BE.txt.txt"
    
    print("SSL下载测试工具")
    print("=" * 80)
    print(f"测试URL: {test_url}")
    print("=" * 80)
    print()
    
    # 解析URL
    parsed_url = urlparse(test_url)
    hostname = parsed_url.hostname
    port = parsed_url.port or 443
    
    print(f"主机名: {hostname}")
    print(f"端口: {port}")
    print(f"协议: {parsed_url.scheme}")
    print()
    
    # 测试1: 基本的socket连接
    print("=== 测试1: 基本socket连接 ===")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((hostname, port))
        print(f"✓ 基本socket连接成功 ({hostname}:{port})")
        sock.close()
    except Exception as e:
        print(f"✗ 基本socket连接失败: {e}")
    print()
    
    # 测试2: SSL socket连接
    print("=== 测试2: SSL socket连接 ===")
    try:
        context = ssl.create_default_context()
        with socket.create_connection((hostname, port), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                print(f"✓ SSL socket连接成功")
                print(f"SSL版本: {ssock.version()}")
                print(f"加密套件: {ssock.cipher()}")
    except Exception as e:
        print(f"✗ SSL socket连接失败: {e}")
    print()
    
    # 测试3: requests GET请求（验证SSL）
    print("=== 测试3: requests GET请求（验证SSL） ===")
    try:
        response = requests.get(test_url, timeout=30)
        print(f"✓ requests GET请求成功")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"内容长度: {len(response.content)} 字节")
        
    except requests.exceptions.SSLError as e:
        print(f"✗ SSL错误: {e}")
    except requests.exceptions.ConnectionError as e:
        print(f"✗ 连接错误: {e}")
    except requests.exceptions.Timeout as e:
        print(f"✗ 超时错误: {e}")
    except Exception as e:
        print(f"✗ 其他错误: {e}")
    print()
    
    # 测试4: requests GET请求（忽略SSL验证）
    print("=== 测试4: requests GET请求（忽略SSL验证） ===")
    try:
        response = requests.get(test_url, verify=False, timeout=30)
        print(f"✓ requests GET请求成功（忽略SSL验证）")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"内容长度: {len(response.content)} 字节")
        
        # 尝试下载一小部分内容
        if response.status_code == 200:
            content_preview = response.content[:200]  # 前200字节
            print(f"内容预览: {content_preview}")
        
    except Exception as e:
        print(f"✗ requests GET请求失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 如果是SSL错误，提供更多信息
        if "SSL" in str(e) or "certificate" in str(e).lower():
            print("\nSSL错误详细信息:")
            print("- 这通常是由于SSL证书验证失败导致的")
            print("- 可能的原因:")
            print("  1. 服务器证书过期或无效")
            print("  2. 证书链不完整")
            print("  3. 系统时间不正确")
            print("  4. CA证书库过期")
            print("  5. 网络代理或防火墙干扰")
            
            print("\n建议解决方案:")
            print("1. 检查系统时间是否正确")
            print("2. 更新系统的CA证书库")
            print("3. 检查网络代理设置")
            print("4. 尝试使用不同的网络环境")
            print("5. 如果是企业网络，联系网络管理员")
    
    print()
    print("=" * 80)
    print("测试完成")

def test_other_https_sites():
    """测试其他HTTPS网站"""
    print("\n=== 测试其他HTTPS网站 ===")
    
    test_sites = [
        "https://www.baidu.com",
        "https://www.google.com",
        "https://httpbin.org/get"
    ]
    
    for site in test_sites:
        print(f"\n测试: {site}")
        try:
            response = requests.get(site, timeout=10, verify=False)
            print(f"✓ 成功 - 状态码: {response.status_code}")
        except Exception as e:
            print(f"✗ 失败: {e}")

def main():
    """主函数"""
    test_problematic_url()
    test_other_https_sites()
    
    print("\n" + "=" * 80)
    print("如果所有测试都失败，说明SSL环境有问题")
    print("如果只有特定URL失败，可能是服务器端的问题")
    print("建议运行 ssl_environment_check.py 进行详细的环境检查")

if __name__ == "__main__":
    main()
