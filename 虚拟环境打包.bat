@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo        虚拟环境打包工具
echo ========================================
echo.
echo 此工具将创建虚拟环境并进行干净的打包
echo 确保只包含必要的依赖，避免SSL模块缺失
echo ========================================
echo.

REM 设置虚拟环境名称
set VENV_NAME=wechat_venv

echo 步骤1: 清理旧的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "%VENV_NAME%" rmdir /s /q "%VENV_NAME%"

echo.
echo 步骤2: 创建虚拟环境...
python -m venv %VENV_NAME%
if errorlevel 1 (
    echo 创建虚拟环境失败，请检查Python安装
    pause
    exit /b 1
)

echo.
echo 步骤3: 激活虚拟环境并升级pip...
call %VENV_NAME%\Scripts\activate.bat
python -m pip install --upgrade pip

echo.
echo 步骤4: 安装项目依赖...
pip install -r requirements.txt

echo.
echo 步骤5: 验证关键模块安装...
echo 检查SSL相关模块...
python -c "import ssl; print('SSL模块:', ssl.OPENSSL_VERSION)"
python -c "import certifi; print('certifi路径:', certifi.where())"
python -c "import requests; print('requests版本:', requests.__version__)"
python -c "import urllib3; print('urllib3版本:', urllib3.__version__)"
python -c "import wxautox; print('wxautox模块正常')"

echo.
echo 步骤6: 运行SSL环境检查...
python ssl_environment_check.py > ssl_check_venv.log 2>&1
echo SSL环境检查结果已保存到 ssl_check_venv.log

echo.
echo 步骤7: 开始PyInstaller打包...
echo 使用虚拟环境进行打包，确保模块完整性...
pyinstaller main_venv.spec

echo.
if exist "dist\main.exe" (
    echo ========================================
    echo           打包成功！
    echo ========================================
    echo 文件位置: dist\main.exe
    echo.
    
    REM 计算文件大小
    for %%A in ("dist\main.exe") do (
        set size=%%~zA
        set /a size_mb=!size! / 1024 / 1024
        echo 文件大小: %%~zA 字节 ^(约 !size_mb! MB^)
    )
    
    echo.
    echo 步骤8: 测试打包后的程序...
    echo 正在测试打包后的SSL功能...
    
    REM 复制测试脚本到dist目录
    copy test_packaged_ssl.py dist\ >nul 2>&1
    
    REM 在dist目录中运行测试
    cd dist
    echo 运行SSL功能测试...
    main.exe --test-ssl > ssl_test_result.log 2>&1
    if errorlevel 1 (
        echo ⚠ 程序启动测试未通过，请查看日志
    ) else (
        echo ✓ 程序启动测试通过
    )
    cd ..
    
    echo.
    echo ========================================
    echo 虚拟环境打包特点:
    echo - 使用干净的虚拟环境，避免依赖冲突
    echo - 只包含必要的模块，减小文件大小
    echo - 确保SSL相关模块完整包含
    echo - 包含完整的证书文件和DLL库
    echo - 自动进行打包后功能测试
    echo.
    echo 如果程序运行正常，可以删除虚拟环境:
    echo rmdir /s /q %VENV_NAME%
    echo ========================================
    
) else (
    echo ========================================
    echo           打包失败！
    echo ========================================
    echo.
    echo 可能的原因:
    echo 1. 虚拟环境创建失败
    echo 2. 依赖安装不完整
    echo 3. PyInstaller配置问题
    echo.
    echo 请检查以下日志文件:
    echo - ssl_check_venv.log ^(SSL环境检查^)
    echo - build\main\warn-main.txt ^(PyInstaller警告^)
    echo.
    echo 建议解决方案:
    echo 1. 检查Python版本是否兼容
    echo 2. 确保网络连接正常
    echo 3. 尝试手动安装依赖: pip install -r requirements.txt
    echo 4. 检查防病毒软件是否阻止了文件创建
)

echo.
echo 按任意键退出...
pause >nul
