#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带SSL支持的打包脚本
确保打包后的程序包含完整的SSL支持
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_requirements():
    """检查打包前的环境要求"""
    print("=== 检查打包环境 ===")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✓ PyInstaller可用，版本: {PyInstaller.__version__}")
    except ImportError:
        print("✗ PyInstaller未安装")
        print("请运行: pip install pyinstaller")
        return False
    
    # 检查SSL模块
    try:
        import ssl
        print(f"✓ SSL模块可用: {ssl.OPENSSL_VERSION}")
    except ImportError:
        print("✗ SSL模块不可用")
        return False
    
    # 检查certifi
    try:
        import certifi
        cert_path = certifi.where()
        if os.path.exists(cert_path):
            print(f"✓ certifi证书可用: {cert_path}")
        else:
            print("✗ certifi证书文件不存在")
            return False
    except ImportError:
        print("✗ certifi未安装")
        print("请运行: pip install certifi")
        return False
    
    # 检查requests
    try:
        import requests
        print(f"✓ requests可用，版本: {requests.__version__}")
    except ImportError:
        print("✗ requests未安装")
        return False
    
    return True

def prepare_build_environment():
    """准备打包环境"""
    print("\n=== 准备打包环境 ===")
    
    # 确保配置文件存在
    if not os.path.exists('config.json'):
        print("创建默认配置文件...")
        default_config = {
            "log_level": "error",
            "last_groups": [],
            "owner_id": "",
            "novel_search_enabled": True,
            "link_parser_enabled": True,
            "shuqi_enabled": True,
            "zhangxinlei_enabled": True,
            "lofter_enabled": True,
            "key": ""
        }
        import json
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=4)
        print("✓ 配置文件已创建")
    else:
        print("✓ 配置文件已存在")
    
    # 清理之前的构建
    build_dirs = ['build', 'dist']
    for build_dir in build_dirs:
        if os.path.exists(build_dir):
            print(f"清理 {build_dir} 目录...")
            shutil.rmtree(build_dir)
    
    print("✓ 构建环境准备完成")

def create_ssl_hook():
    """创建SSL相关的PyInstaller hook"""
    print("\n=== 创建SSL hook ===")
    
    # 创建hooks目录
    hooks_dir = Path("hooks")
    hooks_dir.mkdir(exist_ok=True)
    
    # 创建SSL hook文件
    ssl_hook_content = '''
# SSL相关模块的hook文件
from PyInstaller.utils.hooks import collect_all

# 收集ssl相关的所有模块
datas, binaries, hiddenimports = collect_all('ssl')

# 添加额外的隐藏导入
hiddenimports += [
    '_ssl',
    '_socket',
    'socket',
    'select',
]

# 收集certifi证书
try:
    import certifi
    cert_path = certifi.where()
    if cert_path:
        datas += [(cert_path, 'certifi')]
except ImportError:
    pass
'''
    
    hook_file = hooks_dir / "hook-ssl.py"
    with open(hook_file, 'w', encoding='utf-8') as f:
        f.write(ssl_hook_content)
    
    print(f"✓ SSL hook已创建: {hook_file}")

def build_executable():
    """执行打包"""
    print("\n=== 开始打包 ===")
    
    # 构建命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--onefile',
        '--console',
        '--name', 'wechat_listener',
        '--icon', 'favicon.ico',
        '--additional-hooks-dir', 'hooks',
        '--hidden-import', 'ssl',
        '--hidden-import', '_ssl',
        '--hidden-import', 'socket',
        '--hidden-import', '_socket',
        '--hidden-import', 'certifi',
        '--hidden-import', 'requests',
        '--hidden-import', 'urllib3',
        '--collect-all', 'ssl',
        '--collect-all', 'certifi',
        'main.py'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 打包成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def test_executable():
    """测试打包后的可执行文件"""
    print("\n=== 测试可执行文件 ===")
    
    exe_path = Path("dist") / "wechat_listener.exe"
    if not exe_path.exists():
        print(f"✗ 可执行文件不存在: {exe_path}")
        return False
    
    print(f"✓ 可执行文件已生成: {exe_path}")
    print(f"文件大小: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
    
    # 创建测试脚本
    test_script = '''
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(sys.executable))

try:
    import ssl
    print("SSL模块可用:", ssl.OPENSSL_VERSION)
except Exception as e:
    print("SSL模块错误:", e)

try:
    import certifi
    print("certifi可用:", certifi.where())
except Exception as e:
    print("certifi错误:", e)

try:
    import requests
    print("requests可用:", requests.__version__)
    
    # 测试HTTPS请求
    response = requests.get("https://httpbin.org/get", timeout=5, verify=False)
    print("HTTPS测试成功:", response.status_code)
except Exception as e:
    print("HTTPS测试失败:", e)

input("按回车键退出...")
'''
    
    test_file = Path("test_ssl_in_exe.py")
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"✓ 测试脚本已创建: {test_file}")
    print("请手动运行可执行文件进行测试")
    
    return True

def main():
    """主函数"""
    print("带SSL支持的PyInstaller打包工具")
    print("=" * 60)
    
    # 检查环境
    if not check_requirements():
        print("\n环境检查失败，请解决上述问题后重试")
        return
    
    # 准备环境
    prepare_build_environment()
    
    # 创建hook
    create_ssl_hook()
    
    # 执行打包
    if build_executable():
        test_executable()
        print("\n=== 打包完成 ===")
        print("请测试dist目录下的可执行文件")
        print("如果仍有SSL问题，请运行check_ssl_environment.py进行诊断")
    else:
        print("\n=== 打包失败 ===")
        print("请检查错误信息并重试")

if __name__ == "__main__":
    main()
