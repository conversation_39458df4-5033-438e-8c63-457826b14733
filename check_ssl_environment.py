#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL环境检测脚本
用于检测打包前后的SSL环境是否正常
"""

import os
import sys
import platform

def check_python_environment():
    """检查Python环境"""
    print("=== Python环境信息 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"平台: {platform.platform()}")
    print(f"架构: {platform.architecture()}")
    print()

def check_ssl_module():
    """检查SSL模块"""
    print("=== SSL模块检查 ===")
    
    try:
        import ssl
        print("✓ ssl模块可用")
        print(f"SSL版本: {ssl.OPENSSL_VERSION}")
        print(f"SSL版本号: {ssl.OPENSSL_VERSION_NUMBER}")
        print(f"SSL版本信息: {ssl.OPENSSL_VERSION_INFO}")
        
        # 检查SSL上下文
        try:
            context = ssl.create_default_context()
            print("✓ 可以创建SSL上下文")
            print(f"协议: {context.protocol}")
            print(f"验证模式: {context.verify_mode}")
        except Exception as e:
            print(f"✗ 创建SSL上下文失败: {e}")
            
    except ImportError as e:
        print(f"✗ ssl模块不可用: {e}")
    except Exception as e:
        print(f"✗ ssl模块检查失败: {e}")
    print()

def check_ssl_files():
    """检查SSL相关文件"""
    print("=== SSL文件检查 ===")
    
    # 检查Python目录中的SSL DLL
    python_dir = os.path.dirname(sys.executable)
    ssl_dlls = ['libssl-1_1.dll', 'libcrypto-1_1.dll', 'ssleay32.dll', 'libeay32.dll']
    
    print(f"Python目录: {python_dir}")
    for dll in ssl_dlls:
        dll_path = os.path.join(python_dir, dll)
        if os.path.exists(dll_path):
            print(f"✓ 找到 {dll}: {dll_path}")
        else:
            print(f"✗ 未找到 {dll}")
    
    # 检查DLLs目录
    dlls_dir = os.path.join(python_dir, 'DLLs')
    print(f"\nDLLs目录: {dlls_dir}")
    if os.path.exists(dlls_dir):
        print("✓ DLLs目录存在")
        for dll in ssl_dlls:
            dll_path = os.path.join(dlls_dir, dll)
            if os.path.exists(dll_path):
                print(f"✓ 找到 {dll}: {dll_path}")
            else:
                print(f"✗ 未找到 {dll}")
    else:
        print("✗ DLLs目录不存在")
    print()

def check_certifi():
    """检查certifi证书"""
    print("=== 证书检查 ===")
    
    try:
        import certifi
        cert_path = certifi.where()
        print(f"✓ certifi可用")
        print(f"证书路径: {cert_path}")
        
        if os.path.exists(cert_path):
            print("✓ 证书文件存在")
            file_size = os.path.getsize(cert_path)
            print(f"证书文件大小: {file_size} 字节")
        else:
            print("✗ 证书文件不存在")
            
    except ImportError:
        print("✗ certifi不可用")
    except Exception as e:
        print(f"✗ certifi检查失败: {e}")
    print()

def check_requests():
    """检查requests库"""
    print("=== requests库检查 ===")
    
    try:
        import requests
        import urllib3
        print(f"✓ requests可用，版本: {requests.__version__}")
        print(f"✓ urllib3可用，版本: {urllib3.__version__}")
        
        # 测试简单的HTTPS请求
        try:
            print("测试HTTPS请求...")
            response = requests.get('https://httpbin.org/get', timeout=10, verify=False)
            print(f"✓ HTTPS请求成功，状态码: {response.status_code}")
        except Exception as e:
            print(f"✗ HTTPS请求失败: {e}")
            
    except ImportError as e:
        print(f"✗ requests不可用: {e}")
    except Exception as e:
        print(f"✗ requests检查失败: {e}")
    print()

def check_socket():
    """检查socket模块"""
    print("=== socket模块检查 ===")
    
    try:
        import socket
        print("✓ socket模块可用")
        
        # 测试基本的socket连接
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('www.google.com', 80))
            sock.close()
            
            if result == 0:
                print("✓ socket连接测试成功")
            else:
                print(f"✗ socket连接测试失败，错误码: {result}")
                
        except Exception as e:
            print(f"✗ socket连接测试失败: {e}")
            
    except ImportError as e:
        print(f"✗ socket模块不可用: {e}")
    except Exception as e:
        print(f"✗ socket模块检查失败: {e}")
    print()

def check_encoding():
    """检查编码支持"""
    print("=== 编码支持检查 ===")
    
    encodings_to_test = ['utf-8', 'gbk', 'gb2312', 'big5', 'gb18030']
    
    for encoding in encodings_to_test:
        try:
            import codecs
            codecs.lookup(encoding)
            print(f"✓ {encoding} 编码可用")
        except LookupError:
            print(f"✗ {encoding} 编码不可用")
        except Exception as e:
            print(f"✗ {encoding} 编码检查失败: {e}")
    print()

def main():
    """主函数"""
    print("SSL环境检测工具")
    print("=" * 60)
    print("此工具将检测当前环境的SSL相关配置")
    print("请在打包前后都运行此脚本进行对比")
    print("=" * 60)
    print()
    
    check_python_environment()
    check_ssl_module()
    check_ssl_files()
    check_certifi()
    check_requests()
    check_socket()
    check_encoding()
    
    print("=== 检测完成 ===")
    print("如果打包后某些检查失败，请检查PyInstaller的spec文件配置")
    print("确保所有必要的模块和文件都被正确包含")

if __name__ == "__main__":
    main()
