@echo off
chcp 65001 >nul
echo ========================================
echo        创建虚拟环境
echo ========================================
echo.

set VENV_NAME=wechat_venv

echo 正在创建虚拟环境: %VENV_NAME%
python -m venv %VENV_NAME%

if errorlevel 1 (
    echo 创建虚拟环境失败
    pause
    exit /b 1
)

echo.
echo 激活虚拟环境并安装依赖...
call %VENV_NAME%\Scripts\activate.bat

echo 升级pip...
python -m pip install --upgrade pip

echo 安装项目依赖...
pip install -r requirements.txt

echo.
echo ========================================
echo 虚拟环境创建完成！
echo ========================================
echo.
echo 要激活虚拟环境，请运行:
echo %VENV_NAME%\Scripts\activate.bat
echo.
echo 要在虚拟环境中打包，请运行:
echo 虚拟环境打包.bat
echo.
pause
