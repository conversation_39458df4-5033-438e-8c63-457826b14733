@echo off
chcp 65001 >nul
echo ========================================
echo        测试虚拟环境
echo ========================================
echo.

set VENV_NAME=wechat_venv

if not exist "%VENV_NAME%" (
    echo 虚拟环境不存在，请先运行 创建虚拟环境.bat
    pause
    exit /b 1
)

echo 激活虚拟环境...
call %VENV_NAME%\Scripts\activate.bat

echo.
echo 测试Python环境...
python --version
echo.

echo 测试关键模块...
python -c "import ssl; print('✓ SSL模块正常，版本:', ssl.OPENSSL_VERSION)"
python -c "import certifi; print('✓ certifi正常，证书路径:', certifi.where())"
python -c "import requests; print('✓ requests正常，版本:', requests.__version__)"
python -c "import urllib3; print('✓ urllib3正常，版本:', urllib3.__version__)"
python -c "import wxautox; print('✓ wxautox正常')"

echo.
echo 测试HTTPS连接...
python -c "import requests; r=requests.get('https://www.baidu.com', timeout=10, verify=False); print('✓ HTTPS连接正常，状态码:', r.status_code)"

echo.
echo 运行完整的SSL环境检查...
python ssl_environment_check.py

echo.
echo ========================================
echo 虚拟环境测试完成
echo ========================================
pause
