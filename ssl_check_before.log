SSL������⹤��
============================================================
�˹��߽���⵱ǰ������SSL�������
���ڴ��ǰ�����д˽ű����жԱ�
============================================================

=== Python������Ϣ ===
Python�汾: 3.12.3 (tags/v3.12.3:f6650f9, Apr  9 2024, 14:05:25) [MSC v.1938 64 bit (AMD64)]
Python��ִ���ļ�·��: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
Python��װĿ¼: C:\Users\<USER>\AppData\Local\Programs\Python\Python312
����ϵͳ: Windows 10
�ܹ�: AMD64

=== SSLģ���� ===
Traceback (most recent call last):
  File "D:\wechatlistener\ssl_environment_check.py", line 30, in check_ssl_module
    print(f"\u2713 SSLģ�����")
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\wechatlistener\ssl_environment_check.py", line 202, in <module>
    main()
  File "D:\wechatlistener\ssl_environment_check.py", line 190, in main
    check_ssl_module()
  File "D:\wechatlistener\ssl_environment_check.py", line 47, in check_ssl_module
    print(f"\u2717 SSLģ����ʧ��: {e}")
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence
