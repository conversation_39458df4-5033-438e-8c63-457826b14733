import re
import os
import time
import json
import requests
from novel_finder import Novel<PERSON>inder
from session_manager import <PERSON><PERSON>anager
from config import Config
from owner_manager import OwnerManager
from link_parser import LinkParser

class MessageHandler:
    """消息处理器，负责处理微信消息并实现小说查询功能"""

    # 会话状态常量
    STATE_IDLE = 'idle'  # 空闲状态
    STATE_BROWSING = 'browsing'  # 浏览小说列表状态

    def __init__(self):
        """初始化消息处理器

        Args:
            stories_dir (str): 小说文件夹路径
        """
        self.novel_finder = NovelFinder()
        self.session_manager = SessionManager()
        self.config = Config()
        self.owner_manager = OwnerManager()
        self.link_parser = LinkParser()
        # 使用owner_manager获取功能状态
        self.novel_search_enabled = self.owner_manager.get_feature_status('novel_search_enabled')
        self.link_parser_enabled = self.owner_manager.get_feature_status('link_parser_enabled')
        # 违禁词缓存
        self.banned_words_cache = {
            'data': [],           # 违禁词数据
            'expires_at': 0       # 过期时间戳
        }
        # 初始加载违禁词
        self._load_banned_words()

        # 获取各平台链接解析功能状态
        self.platform_statuses = {
            'shuqi_enabled': self.owner_manager.get_feature_status('shuqi_enabled'),
            'zhangxinlei_enabled': self.owner_manager.get_feature_status('zhangxinlei_enabled'),
            'lofter_enabled': self.owner_manager.get_feature_status('lofter_enabled'),
            'fanqie_toutiao_enabled': self.owner_manager.get_feature_status('fanqie_toutiao_enabled'),
            'zhangyue_enabled': self.owner_manager.get_feature_status('zhangyue_enabled'),
            'fanqie_novel_enabled': self.owner_manager.get_feature_status('fanqie_novel_enabled')
        }

    def process_message(self, sender, content, chat_window, chat_name=None):
        """处理微信消息

        Args:
            sender (str): 发送者
            content (str): 消息内容
            chat_window: 聊天窗口对象
            chat_name (str, optional): 聊天窗口名称

        Returns:
            bool: 是否已处理消息
        """
        # 获取用户会话
        session = self.session_manager.get_session(sender)
        content = content.strip()
        current_state = session.get_state() # 获取初始状态

        # 如果用户在浏览小说状态下发起了新的搜索指令 (以 "找" 开头) 或者发送了链接，则重置会话状态
        # 这样消息可以被后续逻辑作为新的搜索请求或链接解析请求从空闲状态处理
        if current_state == self.STATE_BROWSING:
            # 检查是否是"找"开头的消息
            if content.startswith('找'):
                session.set_state(self.STATE_IDLE)
                session.clear_context()
            # 检查是否包含链接
            elif self.link_parser.is_parse_command(content):
                session.set_state(self.STATE_IDLE)
                session.clear_context()

        # 1. 处理主人命令
        if self.owner_manager.handle_owner_command(sender, content, chat_window):
            # 更新本地状态
            self.novel_search_enabled = self.owner_manager.get_feature_status('novel_search_enabled')
            self.link_parser_enabled = self.owner_manager.get_feature_status('link_parser_enabled')

            # 更新各平台链接解析功能状态
            for platform in self.platform_statuses:
                self.platform_statuses[platform] = self.owner_manager.get_feature_status(platform)

            return True


        # 2. 处理链接解析，检查全局开关和卡密权限 (优先于状态判断，因为解析命令也可能在浏览状态下发出)
        # 获取卡密权限列表
        user_permissions = self.config.get_permissions()
        has_link_parse_permission = "链接解析" in user_permissions

        # 只有当全局开关开启且用户有链接解析权限时才处理链接解析
        if self.link_parser_enabled and has_link_parse_permission and self._handle_link_parsing(content, chat_window, sender):
            # 如果是解析命令，并且成功处理了，那么就直接返回，不再进行后续的状态判断和处理
            # 状态已经在前面被重置为 IDLE (如果之前是 BROWSING)
            return True

        # 3. 根据会话状态处理消息 (此时如果之前是浏览状态且收到解析命令，状态已变为IDLE)
        state = session.get_state() # 重新获取状态，因为可能在前面被修改了
        if state == self.STATE_BROWSING:
            return self._handle_browsing_state(session, content, chat_window, sender)

        # 4. 处理查找小说命令
        search_match = re.match(r'^找\s*(.+)$', content)
        if search_match:
            if not self.novel_search_enabled:
                return True  # 功能关闭，不提示

            keyword = search_match.group(1).strip()
            return self._handle_search_command(session, keyword, chat_window, sender)

        # 没有处理该消息
        return False

    def _handle_link_parsing(self, content, chat_window, sender):
        """处理链接解析

        Args:
            content (str): 消息内容
            chat_window: 聊天窗口对象或微信实例
            sender (str): 发送者

        Returns:
            bool: 是否已处理消息
        """
        # 检查是否是解析命令
        url = self.link_parser.is_parse_command(content)
        if not url:
            return False

        # 已经在process_message中检查了全局开关和权限，这里直接解析链接
        result = self.link_parser.parse_link(content, self.platform_statuses)
        if not result:
            return False

        # 格式化解析结果并发送
        formatted_result = self.link_parser.format_result(result)
        chat_window.SendMsg(formatted_result, at=sender)

        # 如果解析成功并且有文件路径，则发送文件
        if result.get("success") and "file_path" in result:
            chat_window.SendFiles(result["file_path"])

        return True

    def _handle_search_command(self, session, keyword, chat_window, sender):
        """处理查找小说命令

        Args:
            session: 用户会话
            keyword (str): 搜索关键词
            chat_window: 聊天窗口对象或微信实例
            sender (str): 发送者

        Returns:
            bool: 是否已处理消息
        """
        # 搜索小说
        novels = self.novel_finder.search(keyword)

        if not novels:
            chat_window.SendMsg(f"未找到与\"{keyword}\"相关的小说或网络异常,可以再次尝试", at=sender)
            return True

        # 过滤掉包含违禁词的小说
        filtered_novels = []
        for novel in novels:
            name = novel.get("name_all", "")
            # 去除文件后缀名显示小说名
            for ext in ['.zip', '.txt', '.rar', '.pdf']:
                if name.lower().endswith(ext):
                    name = name[:-len(ext)]

            # 如果小说名不包含违禁词，则添加到过滤后的列表中
            if not self._contains_banned_word(name):
                filtered_novels.append(novel)

        # 如果过滤后没有小说，提示用户
        if not filtered_novels:
            chat_window.SendMsg(f"未找到与\"{keyword}\"相关的小说，请尝试其他关键词", at=sender)
            return True

        # 保存过滤后的搜索结果到会话上下文并切换到浏览状态
        session.set_context('novels', filtered_novels)
        session.set_context('keyword', keyword)
        session.set_context('page', 1)
        session.set_context('page_size', 10)
        session.set_state(self.STATE_BROWSING)

        # 发送搜索结果
        self._send_novel_list(session, chat_window, sender)

        return True

    def _handle_browsing_state(self, session, content, chat_window, sender):
        """处理浏览小说列表状态

        Args:
            session: 用户会话
            content (str): 消息内容
            chat_window: 聊天窗口对象
            sender (str): 发送者

        Returns:
            bool: 是否已处理消息
        """
        content = content.strip()
        novels = session.get_context('novels', [])
        page = session.get_context('page', 1)
        page_size = session.get_context('page_size', 10)

        # 使用NovelFinder的方法处理浏览操作
        new_page, novel_info, error_msg = self.novel_finder.handle_browse_action(
            content, novels, page, page_size
        )

        # 处理错误消息
        if error_msg:
            chat_window.SendMsg(error_msg, at=sender)
            return True

        # 页码发生变化，更新会话并显示新页面
        if new_page != page:
            session.set_context('page', new_page)
            self._send_novel_list(session, chat_window, sender)
            return True

        # 选择了小说，下载并发送
        if novel_info:
            # 获取小说名称
            novel_name = novel_info.get("name_all", "")
            # 去除文件后缀名
            for ext in ['.zip', '.txt', '.rar', '.pdf']:
                if novel_name.lower().endswith(ext):
                    novel_name = novel_name[:-len(ext)]

            # 检查文件夹中是否已存在同名文件
            file_name = novel_info.get("name_all", "")
            sanitized_filename = self.novel_finder._sanitize_filename(file_name)
            file_path = os.path.join(self.novel_finder.stories_dir, sanitized_filename)
            already_exists = os.path.exists(file_path)

            # 下载小说或获取本地文件路径
            novel_path = self.novel_finder.download_novel(novel_info)

            if novel_path:
                chat_window.SendMsg(f"发送: {novel_name}", at=sender)

                # 发送文件
                chat_window.SendFiles(novel_path)

                # 发送文件后直接退出浏览状态，不再显示菜单
                session.set_state(self.STATE_IDLE)
                session.clear_context()
            else:
                chat_window.SendMsg("小说下载失败，请尝试其他小说或查找新的小说", at=sender)

            return True

        # 如果走到这里，说明是其他命令，交给其他处理器处理
        return False

    def _load_banned_words(self):
        """从API加载违禁词列表
        每1小时更新一次违禁词列表，如果内存缓存中有数据且未过期则使用缓存
        """
        current_time = time.time()

        # 检查缓存是否有效（未过期）
        if self.banned_words_cache['expires_at'] > current_time:
            # 缓存有效，直接使用
            return

        # 缓存已过期或不存在，从API获取
        try:
            # 从API获取违禁词列表
            response = requests.get(self.config.server_base_url+"/api/banned_words.php", timeout=5)
            if response.status_code == 200:
                # 使用 utf-8-sig 解码以处理 BOM
                try:
                    decoded_content = response.content.decode('utf-8-sig')
                    data = json.loads(decoded_content)
                except json.JSONDecodeError as json_err:
                    data = None # 或者进行其他错误处理
                if data.get("code") == 200 and "data" in data:
                    # 更新缓存数据
                    self.banned_words_cache['data'] = data["data"]
                    # 设置过期时间为1小时后
                    self.banned_words_cache['expires_at'] = current_time + 3600
        except Exception as e:
            # 如果API请求失败但缓存中有数据，延长缓存时间10分钟
            if self.banned_words_cache['data']:
                self.banned_words_cache['expires_at'] = current_time + 600  # 10分钟

    def _contains_banned_word(self, text):
        """检查文本是否包含违禁词

        Args:
            text (str): 要检查的文本

        Returns:
            bool: 是否包含违禁词
        """
        # 确保违禁词列表是最新的
        self._load_banned_words()

        # 如果违禁词列表为空，则不过滤
        if not self.banned_words_cache['data']:
            return False

        # 检查文本是否包含任何违禁词
        for word in self.banned_words_cache['data']:
            if word in text:
                return True
        return False

    def _send_novel_list(self, session, chat_window, sender):
        """发送小说列表

        Args:
            session: 用户会话
            chat_window: 聊天窗口对象
            sender (str): 发送者
        """
        novels = session.get_context('novels', [])
        page = session.get_context('page', 1)
        page_size = session.get_context('page_size', 10)

        # 使用NovelFinder的方法格式化显示搜索结果
        message = self.novel_finder.display_search_results(novels, page, page_size)

        # 发送消息
        chat_window.SendMsg(message, at=sender)