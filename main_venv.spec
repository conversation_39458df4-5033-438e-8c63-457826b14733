# -*- mode: python ; coding: utf-8 -*-
"""
虚拟环境专用的PyInstaller配置文件
确保在干净的虚拟环境中打包，包含完整的SSL支持
"""

import os
import sys

# 获取certifi证书路径
try:
    import certifi
    cert_path = certifi.where()
    cert_datas = [(cert_path, 'certifi')]
    print(f"✓ 找到certifi证书: {cert_path}")
except ImportError:
    cert_datas = []
    print("✗ certifi未安装")

# 获取SSL相关的二进制文件
ssl_binaries = []
if sys.platform.startswith('win'):
    # Windows平台的SSL库
    try:
        import ssl
        print(f"✓ SSL版本: {ssl.OPENSSL_VERSION}")
        
        # 查找可能的SSL DLL文件
        possible_ssl_files = [
            'libssl-1_1.dll',
            'libcrypto-1_1.dll', 
            'libssl-1_1-x64.dll',
            'libcrypto-1_1-x64.dll',
            'libssl-3.dll',
            'libcrypto-3.dll',
            'libssl-3-x64.dll',
            'libcrypto-3-x64.dll',
            'ssleay32.dll',
            'libeay32.dll'
        ]
        
        # 在Python安装目录中查找SSL文件
        python_dir = os.path.dirname(sys.executable)
        search_dirs = [
            python_dir,
            os.path.join(python_dir, 'DLLs'),
            os.path.join(python_dir, 'Library', 'bin'),  # conda环境
        ]
        
        for search_dir in search_dirs:
            if os.path.exists(search_dir):
                for dll_name in possible_ssl_files:
                    dll_path = os.path.join(search_dir, dll_name)
                    if os.path.exists(dll_path):
                        ssl_binaries.append((dll_path, '.'))
                        print(f"✓ 找到SSL文件: {dll_path}")
        
        if not ssl_binaries:
            print("⚠ 未找到SSL DLL文件，可能是静态链接")
            
    except Exception as e:
        print(f"✗ SSL检查失败: {e}")

# 添加其他可能需要的二进制文件
additional_binaries = []

# 检查wxautox相关的DLL
try:
    import wxautox
    wxautox_path = os.path.dirname(wxautox.__file__)
    print(f"✓ wxautox路径: {wxautox_path}")
    
    # 查找wxautox可能需要的DLL文件
    for root, dirs, files in os.walk(wxautox_path):
        for file in files:
            if file.endswith('.dll'):
                dll_path = os.path.join(root, file)
                additional_binaries.append((dll_path, '.'))
                print(f"✓ 找到wxautox DLL: {dll_path}")
                
except Exception as e:
    print(f"⚠ wxautox检查失败: {e}")

# 合并所有二进制文件
all_binaries = ssl_binaries + additional_binaries

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=all_binaries,
    datas=cert_datas,
    # 确保必要的模块被包含，特别是SSL和网络相关模块
    hiddenimports=[
        # 核心SSL和加密模块
        'ssl',
        '_ssl',
        '_hashlib',
        '_socket',
        'socket',
        'certifi',
        'certifi.core',
        
        # HTTP请求核心模块
        'requests',
        'requests.adapters',
        'requests.auth',
        'requests.cookies',
        'requests.models',
        'requests.sessions',
        'requests.structures',
        'requests.utils',
        'requests.packages',
        'requests.packages.urllib3',
        
        # urllib3完整模块
        'urllib3',
        'urllib3.util',
        'urllib3.util.retry',
        'urllib3.util.connection',
        'urllib3.util.ssl_',
        'urllib3.util.timeout',
        'urllib3.util.url',
        'urllib3.connection',
        'urllib3.connectionpool',
        'urllib3.poolmanager',
        'urllib3.response',
        'urllib3.exceptions',
        'urllib3.contrib',
        'urllib3.contrib.pyopenssl',
        
        # 标准库网络模块
        'urllib',
        'urllib.request',
        'urllib.parse',
        'urllib.error',
        'http',
        'http.client',
        'http.server',
        'http.cookies',
        
        # 微信自动化相关
        'wxautox',
        'comtypes',
        'comtypes.client',
        'win32api',
        'win32con',
        'win32gui',
        'win32process',
        'win32clipboard',
        'pywintypes',
        
        # 标准库基础模块
        'enum',
        'functools',
        'collections',
        'collections.abc',
        'typing',
        'json',
        'json.decoder',
        'json.encoder',
        'threading',
        '_thread',
        'hashlib',
        're',
        'sre_compile',
        'sre_parse',
        'sre_constants',
        
        # 编码相关
        'codecs',
        'encodings',
        'encodings.utf_8',
        'encodings.gbk',
        'encodings.gb2312',
        'encodings.gb18030',
        'encodings.big5',
        'encodings.ascii',
        'encodings.latin_1',
        
        # 时间和日期
        'datetime',
        'time',
        'calendar',
        
        # 文件和路径操作
        'os',
        'os.path',
        'pathlib',
        'shutil',
        'tempfile',
        'glob',
        
        # 随机数和数学
        'random',
        'math',
        'statistics',
        
        # 系统相关
        'platform',
        'sys',
        'subprocess',
        'signal',
        
        # 图像处理（PIL/Pillow）
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        'PIL.ImageGrab',
        
        # 其他可能需要的模块
        'logging',
        'logging.handlers',
        'configparser',
        'argparse',
        'traceback',
        'warnings',
        'weakref',
        'copy',
        'pickle',
        'base64',
        'binascii',
        'struct',
        'array',
        'io',
        'contextlib',
        'itertools',
        'operator',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    # 在虚拟环境中，我们可以更保守地排除模块
    excludes=[
        # 排除明确不需要的大型库
        'torch', 'tensorflow', 'numpy', 'scipy', 'pandas', 'sklearn',
        'cv2', 'opencv', 'sympy', 'networkx', 'keras', 'h5py',
        'matplotlib', 'seaborn', 'plotly',
        
        # 排除GUI框架（保留PIL）
        'tkinter', 'turtle', 'idlelib',
        'tcl', 'tk', '_tkinter',
        'PyQt5', 'PyQt6', 'PySide2', 'PySide6',
        'kivy', 'pygame',
        
        # 排除测试框架
        'unittest', 'test', 'tests', 'pytest', 'nose', 'doctest',
        'coverage', 'mock',
        
        # 排除开发工具
        'pdb', 'profile', 'pstats', 'trace', 'pydoc', 'pygments',
        'black', 'flake8', 'mypy', 'pylint',
        
        # 排除Jupyter/IPython
        'IPython', 'jupyter', 'notebook', 'ipykernel',
        
        # 排除Web框架
        'django', 'flask', 'fastapi', 'tornado', 'aiohttp',
        'bottle', 'cherrypy', 'pyramid',
        
        # 排除数据库驱动
        'psycopg2', 'pymongo', 'sqlalchemy', 'mysql',
        'sqlite3',  # 如果不需要数据库功能
        
        # 排除编译器
        'nuitka', 'cython',
        
        # 排除其他大型库
        'fontTools', 'reportlab', 'openpyxl', 'xlsxwriter',
        'lxml', 'beautifulsoup4', 'scrapy', 'selenium',
    ],
    noarchive=False,
    optimize=1,  # 中等级别的Python字节码优化
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='main',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # 启用符号表剥离以减小文件大小
    upx=False,   # 不使用UPX压缩，确保兼容性
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['favicon.ico'] if os.path.exists('favicon.ico') else None,
)
